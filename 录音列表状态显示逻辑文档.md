# 录音列表转写及总结状态显示逻辑文档

## 概述

录音列表页面需要显示每个录音文件的处理状态，包括转写和总结的进度。由于业务逻辑限制（转写和总结不会同时进行），使用统一的状态控件来显示当前的处理状态。

## 状态枚举定义

### RecordingProcessStatus
```kotlin
enum class RecordingProcessStatus {
    NOT_STARTED,           // 未开始处理
    UPLOADING,            // 上传中
    TRANSCRIBING,         // 转写中
    TRANSCRIPTION_COMPLETED, // 转写完成
    TRANSCRIPTION_FAILED,    // 转写失败
    SUMMARIZING,          // 总结中
    SUMMARY_COMPLETED,    // 总结完成
    SUMMARY_FAILED        // 总结失败
}
```

## 状态判断逻辑

### RecordingProcessStatusHelper.getProcessStatus()

状态判断优先级：**转写状态 > 总结状态**

#### 判断流程：

1. **检查转写状态**
   - 如果 `transcriptionId` 为空 → `NOT_STARTED`
   - 如果后台服务正在转写 → `TRANSCRIBING`
   - 如果转写内容为空 → `TRANSCRIPTION_FAILED`

2. **转写完成后检查总结状态**
   - 如果后台服务正在总结 → `SUMMARIZING`
   - 如果有总结任务ID但无总结内容且无错误 → `SUMMARIZING`（轮询中）
   - 如果有总结内容且无错误 → `SUMMARY_COMPLETED`
   - 如果有错误码 → `SUMMARY_FAILED`
   - 其他情况 → `TRANSCRIPTION_COMPLETED`

#### 关键数据字段：
- `transcriptionId`: 转写任务ID
- `srcStr`: 转写内容
- `summaryTaskId`: 总结任务ID
- `summaryStr`: 总结内容
- `summaryErrorCode`: 总结错误码

## 状态管理架构

### ViewModel层 (RecordingListViewModel)

```kotlin
// 状态存储
private val processStatusMap = ConcurrentHashMap<String, String>()
private val _processStatusFlow = MutableStateFlow<Map<String, String>>(emptyMap())
val processStatusFlow: StateFlow<Map<String, String>> = _processStatusFlow

// 状态管理方法
fun updateProcessStatus(filePath: String, status: String)
fun getProcessStatus(filePath: String): String?
fun clearProcessStatus(filePath: String)
```

### Fragment层 (NormalRecordingListFragment)

#### 状态初始化
```kotlin
private fun initTranscriptionStatus() {
    lifecycleScope.launch {
        viewModel.audioMutableList.collect { audioList ->
            audioList.forEach { audioBean ->
                val processStatus = RecordingProcessStatusHelper.getProcessStatus(audioBean)
                viewModel.updateProcessStatus(audioBean.path, processStatus.name)
            }
        }
    }
}
```

#### 回调方法状态更新

**转写相关回调：**
- `onUploadStarted` → `UPLOADING`
- `onUploadFailed` → `TRANSCRIPTION_FAILED`
- `onTranscriptionStarted` → `TRANSCRIBING`
- `onTranscriptionSuccess` → `TRANSCRIPTION_COMPLETED`
- `onTranscriptionFailed` → `TRANSCRIPTION_FAILED`

**总结相关回调：**
- `onSummaryStarted` → `SUMMARIZING`
- `onSummarySuccess` → `SUMMARY_COMPLETED`
- `onSummaryFailed` → `SUMMARY_FAILED`

## UI显示组件

### ProcessStatusIndicator组件

根据状态显示不同的UI元素：

#### 进行中状态（显示加载动画）
- `UPLOADING`: 橙色加载动画 + "上传中"
- `TRANSCRIBING`: 蓝色加载动画 + "转写中"
- `SUMMARIZING`: 橙色加载动画 + "总结中"

#### 成功状态（显示成功图标）
- `TRANSCRIPTION_COMPLETED`: 绿色成功图标 + "转写成功"
- `SUMMARY_COMPLETED`: 橙色成功图标 + "总结成功"

#### 失败状态（显示错误图标）
- `TRANSCRIPTION_FAILED`: 红色错误图标 + "转写失败"
- `SUMMARY_FAILED`: 红色错误图标 + "总结失败"

#### 未开始状态
- `NOT_STARTED`: 不显示任何内容

### 颜色方案
- 上传中：`ColorD26913`（橙色）
- 转写中/转写成功：`Color55D8E4`（蓝色）
- 总结中/总结成功：`ColorFF8C45`（橙色）
- 失败状态：`ColorFF0050`（红色）

## 后台服务集成

### BackgroundTranscriptionService

提供状态查询方法：
- `isTranscribing(filePath: String)`: 检查是否正在转写
- `isSummarizingByFilePath(filePath: String)`: 检查是否正在总结

这些方法用于实时判断后台任务状态，确保UI显示的准确性。

## 业务流程

### 完整处理流程
1. 用户开始转写 → `UPLOADING`
2. 上传完成 → `TRANSCRIBING`
3. 转写完成 → `TRANSCRIPTION_COMPLETED`
4. 用户开始总结 → `SUMMARIZING`
5. 总结完成 → `SUMMARY_COMPLETED`

### 异常处理流程
- 上传失败 → `TRANSCRIPTION_FAILED`
- 转写失败 → `TRANSCRIPTION_FAILED`
- 总结失败 → `SUMMARY_FAILED`

## 关键特性

1. **状态互斥性**: 转写和总结不会同时进行，UI只显示当前活跃的状态
2. **优先级明确**: 转写状态优先于总结状态显示
3. **实时更新**: 通过StateFlow实现状态的实时更新
4. **持久化**: 状态信息基于数据库中的实际数据计算，重启应用后状态正确
5. **用户友好**: 清晰的图标和文字提示，用户可以直观了解处理进度

## 注意事项

1. **状态一致性**: 确保回调方法中的状态更新与实际业务状态保持一致
2. **错误处理**: 各种异常情况都有对应的失败状态显示
3. **性能考虑**: 使用ConcurrentHashMap确保多线程环境下的状态管理安全
4. **UI响应**: 通过StateFlow确保UI能及时响应状态变化

## 测试建议

1. **状态转换测试**: 验证各种状态之间的正确转换
2. **异常情况测试**: 测试网络异常、服务异常等情况下的状态显示
3. **并发测试**: 测试多个录音同时处理时的状态管理
4. **重启测试**: 验证应用重启后状态显示的正确性
