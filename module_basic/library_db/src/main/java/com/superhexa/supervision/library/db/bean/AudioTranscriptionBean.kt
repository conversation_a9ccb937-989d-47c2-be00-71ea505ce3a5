package com.superhexa.supervision.library.db.bean

import androidx.annotation.Keep
import androidx.compose.runtime.mutableStateOf
import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id

@Entity
@Keep
data class AudioTranscriptionBean(
    @Id var objId: Long = 0L,
    val id: Int,
    var path: String,
    var userId: String = "",
    var transcriptionId: String? = null,
    var summaryTaskId: String? = null,
    var srcLang: String? = null,
    var srcStr: String? = null,
    var summaryStr: String? = null,
    var isFirstShow: Boolean = true,
    var isDistinguishSpeakers: Boolean = true,
    var fileCreateTime: Long = 0L,
    var speakerName: String? = "",
    var summaryTitle: String? = "",
    var summaryTemplate: String? = null,
    var summaryErrorCode: Int = 0,
    /**
     * 录音处理状态，用于在app重启后恢复状态显示
     * 可能的值：NOT_STARTED, UPLOADING, TRANSCRIBING, TRANSCRIPTION_COMPLETED,
     * TRANSCRIPTION_FAILED, SUMMARIZING, SUMMARY_COMPLETED, SUMMARY_FAILED
     * 默认为空字符串，表示未设置状态
     */
    var processStatus: String = ""
) {

    @delegate:Transient
    val srtContent by lazy { mutableStateOf(srcStr) }

    @delegate:Transient
    val summaryContent by lazy { mutableStateOf(summaryStr) }
}
